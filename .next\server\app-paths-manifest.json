{"/api/admin/candidates/route": "app/api/admin/candidates/route.js", "/api/upload/route": "app/api/upload/route.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/admin/dashboard/route": "app/api/admin/dashboard/route.js", "/_not-found/page": "app/_not-found/page.js", "/auth/signin/page": "app/auth/signin/page.js", "/admin/candidates/new/page": "app/admin/candidates/new/page.js", "/admin/candidates/page": "app/admin/candidates/page.js", "/admin/page": "app/admin/page.js"}