import { NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { candidates, testResults } from '@/lib/db/schema';
import { eq, desc, count } from 'drizzle-orm';

export async function GET() {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get total candidates
    const totalCandidatesResult = await db
      .select({ count: count() })
      .from(candidates);
    const totalCandidates = totalCandidatesResult[0]?.count || 0;

    // Get total results
    const totalResultsResult = await db
      .select({ count: count() })
      .from(testResults);
    const totalResults = totalResultsResult[0]?.count || 0;

    // Get pending results
    const pendingResultsResult = await db
      .select({ count: count() })
      .from(testResults)
      .where(eq(testResults.status, 'pending'));
    const pendingResults = pendingResultsResult[0]?.count || 0;

    // Get completed results
    const completedResultsResult = await db
      .select({ count: count() })
      .from(testResults)
      .where(eq(testResults.status, 'completed'));
    const completedResults = completedResultsResult[0]?.count || 0;

    // Get recent candidates
    const recentCandidates = await db
      .select({
        id: candidates.id,
        fullName: candidates.fullName,
        email: candidates.email,
        createdAt: candidates.createdAt,
      })
      .from(candidates)
      .orderBy(desc(candidates.createdAt))
      .limit(10);

    // Get recent results with candidate info
    const recentResults = await db
      .select({
        id: testResults.id,
        overallBandScore: testResults.overallBandScore,
        status: testResults.status,
        createdAt: testResults.createdAt,
        candidate: {
          fullName: candidates.fullName,
        },
      })
      .from(testResults)
      .innerJoin(candidates, eq(testResults.candidateId, candidates.id))
      .orderBy(desc(testResults.createdAt))
      .limit(10);

    return NextResponse.json({
      totalCandidates,
      totalResults,
      pendingResults,
      completedResults,
      recentCandidates,
      recentResults,
    });
  } catch (error) {
    console.error('Dashboard stats error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
